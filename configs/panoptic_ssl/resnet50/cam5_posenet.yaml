CUDNN:
  BENCHMARK: false
  DETERMINISTIC: false
  ENABLED: true
BACKBONE_MODEL: 'pose_resnet'
MODEL: 'multi_person_posenet_ssv'
DATA_DIR: ''
GPUS: '0'
OUTPUT_DIR: './results/adaptOR3D'
LOG_DIR: './results/adaptOR3D'
WORKERS: 6
PRINT_FREQ: 100
WITH_SSV: True
WITH_ATTN: True
ATTN_WEIGHT: 0.1
ATTN_NUM_LAYERS: 18
USE_L1: True
L1_WEIGHT: 0.01
L1_ATTN: True

DATASET:
  COLOR_RGB: True
  TRAIN_DATASET: 'panoptic_ssv'
  TEST_DATASET: 'panoptic'
  DATA_FORMAT: jpg
  DATA_AUGMENTATION: False
  SUFFIX: "pseudo_hrnet_soft_9videos"
  FLIP: False
  ROOT: 'data/panoptic-toolbox/data/' # 'data/panoptic/'
  ROT_FACTOR1: 45
  ROT_FACTOR2: 45
  SCALE_FACTOR1: 0.35
  SCALE_FACTOR2: 0.35
  APPLY_CUTOUT: True
  APPLY_RANDAUG: True
  TEST_SUBSET: 'validation'
  TRAIN_SUBSET: 'train'
  ROOTIDX: 2
  CAMERA_NUM: 5
NETWORK:
  PRETRAINED_BACKBONE: "models/backbone_epoch20.pth.tar"
  PRETRAINED:  ""  # 'models/pytorch/imagenet/resnet50-19c8e357.pth'
  TARGET_TYPE: gaussian
  INIT_ROOTNET: "models/cam5_rootnet_epoch2.pth.tar"
  INIT_ALL: ""
  TRAIN_BACKBONE: True
  TRAIN_ONLY_ROOTNET: False
  ROOTNET_TRAIN_SYNTH: True
  FREEZE_ROOTNET: True
  PRETRAINED_BACKBONE_PSEUDOGT: True
  ROOTNET_BUFFER_SIZE: 10000
  IMAGE_SIZE_ORIG:
  - 1920
  - 1080  
  IMAGE_SIZE:
  - 960
  - 512
  HEATMAP_SIZE:
  - 240
  - 128
  SIGMA: 3
  NUM_JOINTS: 15
  USE_GT: False
  ROOTNET_ROOTHM: True
POSE_RESNET:
  FINAL_CONV_KERNEL: 1
  DECONV_WITH_BIAS: False
  NUM_DECONV_LAYERS: 3
  NUM_DECONV_FILTERS:
  - 256
  - 256
  - 256
  NUM_DECONV_KERNELS:
  - 4
  - 4
  - 4
  NUM_LAYERS: 50
LOSS:
  USE_TARGET_WEIGHT: true
TRAIN:
  BATCH_SIZE: 1
  SHUFFLE: true
  BEGIN_EPOCH: 0
  END_EPOCH: 8
  RESUME: true
  OPTIMIZER: adam
  LR: 0.0001
  LR_FACTOR: 0.1
  LR_STEP: [5,7]
  L1_EPOCH: 5
TEST:
  MODEL_FILE: 'model_best.pth.tar'
  BATCH_SIZE: 1
DEBUG:
  DEBUG: true
  SAVE_HEATMAPS_GT: true
  SAVE_HEATMAPS_PRED: true
  SAVE_3D_POSES: true
  SAVE_3D_ROOTS: true
MULTI_PERSON:
  SPACE_SIZE:
    - 8000.0
    - 8000.0
    - 2000.0
  SPACE_CENTER:
    - 0.0 # 120.0
    - -500.0 # -600.0
    - 800.0
  INITIAL_CUBE_SIZE:
    - 80
    - 80
    - 20
  MAX_PEOPLE_NUM: 10
  THRESHOLD: 0.3
PICT_STRUCT:
  GRID_SIZE:
    - 2000.0
    - 2000.0
    - 2000.0
  CUBE_SIZE:
    - 64
    - 64
    - 64
