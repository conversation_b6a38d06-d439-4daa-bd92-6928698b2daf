# ------------------------------------------------------------------------------
# Copyright (c) Microsoft Corporation. All rights reserved.
# Licensed under the MIT License.
# ------------------------------------------------------------------------------

from __future__ import absolute_import
from __future__ import division
from __future__ import print_function
import sys

import numpy as np
import torch
import torch.backends.cudnn as cudnn
import torch.utils.data
import torch.utils.data.distributed
import torchvision.transforms as transforms
import argparse
import os
import time
from tqdm import tqdm
from prettytable import PrettyTable
import copy
import logging

import _init_paths
from core.config import config
from core.config import update_config
from utils.utils import create_logger, load_backbone_panoptic
import dataset
import models
from utils.vis import save_batch_heatmaps_multi


def parse_args():
    parser = argparse.ArgumentParser(description='Train keypoints network')
    parser.add_argument('--cfg', help='experiment configure file name', required=True, type=str)
    parser.add_argument('--with-ssv', dest='with_ssv', action='store_true')
    parser.add_argument('--vis-attn', dest='vis_attn', action='store_true')
    parser.add_argument(
        '--test-file', help='test_file', required=True, type=str)
    parser.add_argument(
        '--dtype', help='dtype', required=True, type=str)
    args, rest = parser.parse_known_args()
    update_config(args.cfg)

    return args


def main():
    args = parse_args()
    with_ssv = args.with_ssv
    vis_attn = args.vis_attn
    cfg_name = os.path.basename(args.cfg).split('.')[0]
    final_output_dir = os.path.join("./results_publ/", cfg_name)
    os.makedirs(final_output_dir, exist_ok=True)
    log_file = '{}_{}_{}.log'.format(cfg_name, time.strftime('%Y-%m-%d-%H-%M'),  'eval_map')
    final_log_file = os.path.join(final_output_dir, log_file)
    head = '%(asctime)-15s %(message)s'
    logging.basicConfig(filename=str(final_log_file),
                        format=head)
    logger = logging.getLogger()
    logger.setLevel(logging.INFO)
    console = logging.StreamHandler()
    logging.getLogger('').addHandler(console)                        

    gpus = [int(i) for i in config.GPUS.split(',')]
    #gpus = [0]
    print('=> Loading data ..')
    normalize = transforms.Normalize(
        mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])

    test_dataset = eval('dataset.' + config.DATASET.TEST_DATASET)(
        config, config.DATASET.TEST_SUBSET, False,
        transforms.Compose([
            transforms.ToTensor(),
            normalize,
        ]))

    test_loader = torch.utils.data.DataLoader(
        test_dataset,
        batch_size=config.TEST.BATCH_SIZE * len(gpus),
        shuffle=False,
        num_workers=config.WORKERS,
        pin_memory=True)

    cudnn.benchmark = config.CUDNN.BENCHMARK
    torch.backends.cudnn.deterministic = config.CUDNN.DETERMINISTIC
    torch.backends.cudnn.enabled = config.CUDNN.ENABLED

    print('=> Constructing models ..')
    model = eval('models.' + config.MODEL + '.get_multi_person_pose_net')(
        config, is_train=False)
    with torch.no_grad():
        model = torch.nn.DataParallel(model, device_ids=gpus).cuda()

    if os.path.isfile(args.test_file):
        test_model_file = args.test_file
    else:
        test_model_file = os.path.join(final_output_dir, config.TEST.MODEL_FILE)
    logger.info('=> test_model_file {}'.format(test_model_file))
    if config.TEST.MODEL_FILE and os.path.isfile(test_model_file):
        logger.info('=> load models state {}'.format(test_model_file))
        model.module.load_state_dict(torch.load(test_model_file))
    else:
        raise ValueError('Check the model file for testing!')

    model.eval()

    ### TRTModule INT8
    from torch2trt import torch2trt, TRTModule, trt
    import json

    class ImageBatchStream(trt.IInt8EntropyCalibrator2):
        def __init__(self, mode):
            super(ImageBatchStream, self).__init__()
            self.mode = mode
            self.index = 0
            self.db = self.get_db()
            self.batch = None  # 배치 텐서에 대한 참조를 유지하기 위한 변수
            print(f"✅ [Calibrator] Initialized for '{self.mode}'. Found {len(self.db)} files.")
        
        def get_db(self):
            db = []
            for i in range(500):
                db.append(f'calibration_data/{self.mode}_{i}.npy')
            return db

        def get_batch_size(self):
            # 이 메서드는 torch2trt에 의해 호출됩니다.
            print("✅ [Calibrator] get_batch_size() called, returning 1.")
            return 1  # 배치 사이즈

        def get_batch(self, names):
            if self.index >= len(self.db):
                print("✅ [Calibrator] get_batch() finished. All data processed.")
                return None
            
            filepath = self.db[self.index]
            data = np.load(filepath)
            if self.mode == 'pose':
                data = data.unsqueeze(0)
            self.batch = torch.from_numpy(data.astype(np.float32)).cuda()
            
            print(f"➡️  [Calibrator] get_batch() called for index {self.index}. Loading '{filepath}'. Shape: {self.batch.shape}, Device: {self.batch.device}")
            
            self.index += 1
            return [self.batch.data_ptr()]

        def read_calibration_cache(self):
            cache_file = f'calib_cache_{self.mode}.bin'
            if os.path.exists(cache_file):
                print(f"✅ [Calibrator] Reading calibration cache: {cache_file}")
                with open(cache_file, 'rb') as f:
                    return f.read()
            print("ℹ️ [Calibrator] Calibration cache not found.")
            return None


        def write_calibration_cache(self, cache):
            cache_file = f'calib_cache_{self.mode}.bin'
            print(f"✅ [Calibrator] Writing calibration cache: {cache_file}")
            with open(cache_file, 'wb') as f:
                f.write(cache)
        
    # torch style calibration dataset
    class CalibrationDataset(torch.utils.data.Dataset):
        def __init__(self, mode, batch_size):
            self.mode = mode
            self.db = self.get_db()
            self.batch = batch_size  # 배치 텐서에 대한 참조를 유지하기 위한 변수
            self.index = 0
        
        def get_db(self):
            db = []
            for i in range(500):
                db.append(f'calibration_data/{self.mode}_{i}.npy')
            return db
        
        def __getitem__(self, index):
            # batch 만큼 쌓기
            if index >= len(self.db):
                raise IndexError
            for i in range(self.batch):
                if index >= len(self.db):
                    raise IndexError
                data = np.load(self.db[index])
                if self.mode == 'pose':
                    data = data.unsqueeze(0)
        
        def __len__(self):
            return len(self.db) // self.batch

    # calibration_data = {
    #     'backbone': [],
    #     'root_v2v_net': [],
    #     'pose_v2v_net': [],
    # }
    # bidx = 0
    # root_idx = 0
    # pose_idx = 0
    # os.makedirs('calibration_data', exist_ok=True)
    # for i, (inputs, targets_2d, weights_2d, targets_3d, meta, input_heatmap) in enumerate(tqdm(test_loader)):
    #     if 'panoptic' in config.DATASET.TEST_DATASET:
    #         pred, heatmaps, grid_centers, initial_cubes, all_cubes = model(views1=inputs, meta1=meta, inference=True)
    #         inputs = [input.detach().cpu().numpy() for input in inputs]
    #         initial_cubes = initial_cubes.detach().cpu().numpy()
    #         all_cubes = [cube.detach().cpu().numpy() for cube in all_cubes]
    #         for input in inputs:
    #             if bidx >= 500:
    #                 continue
    #             np.save(f'calibration_data/backbone_{bidx}.npy', input)
    #             bidx += 1
    #         for cube in all_cubes:
    #             if pose_idx >= 500:
    #                 continue
    #             np.save(f'calibration_data/pose_{pose_idx}.npy', cube)
    #             pose_idx += 1
    #         if root_idx >= 500:
    #             break
    #         np.save(f'calibration_data/root_{root_idx}.npy', initial_cubes)
    #         root_idx += 1
    # print('calibration data saved')

    # Backbone
    if args.dtype != 'fp32':
        if os.path.exists(f'backbone_trt_{args.dtype}_{config.TEST.BATCH_SIZE}.pth'):
            backbone_trt = TRTModule()
            backbone_trt.load_state_dict(torch.load(f'backbone_trt_{args.dtype}_{config.TEST.BATCH_SIZE}.pth'))
            backbone_trt.eval()
        else:
            print('trt backbone')
            x = torch.ones(config.TEST.BATCH_SIZE, 3, 512, 960).cuda()
            backbone = model.module.backbone
            if args.dtype == 'int8':
                calibration_data = CalibrationDataset('backbone', config.TEST.BATCH_SIZE)
                backbone_trt = torch2trt(
                    backbone,
                    [x],
                    int8_mode=True,
                    # int8_calib_dataset=calibration_data,
                    use_onnx=True,
                    max_batch_size=1,
                )
            elif args.dtype == 'fp16':
                backbone_trt = torch2trt(
                    backbone,
                    [x],
                    # fp16_mode=True,
                    use_onnx=True,
                )

            torch.save(backbone_trt.state_dict(), f'backbone_trt_{args.dtype}_{config.TEST.BATCH_SIZE}.pth')
            print(f'backbone_trt {args.dtype}_{config.TEST.BATCH_SIZE} saved')
        model.module.backbone = backbone_trt
        print('backbone_trt set')

    # Root
    if args.dtype != 'fp32':
        if os.path.exists(f'root_v2v_net_trt_{args.dtype}_{config.TEST.BATCH_SIZE}.pth'):
            root_v2v_net_trt = TRTModule()
            root_v2v_net_trt.load_state_dict(torch.load(f'root_v2v_net_trt_{args.dtype}_{config.TEST.BATCH_SIZE}.pth'))
            root_v2v_net_trt.eval()
        else:
            print('trt root_v2v_net')
            x = torch.ones(config.TEST.BATCH_SIZE, 1, 80, 80, 20).cuda()
            root_v2v_net = model.module.root_net.v2v_net
            if args.dtype == 'int8':
                calibration_data = CalibrationDataset('root', config.TEST.BATCH_SIZE)
                root_v2v_net_trt = torch2trt(
                    root_v2v_net,
                    [x],
                    int8_mode=True,
                    # int8_calib_dataset=calibration_data,
                    use_onnx=True,
                    max_batch_size=1,
                )
            elif args.dtype == 'fp16':
                root_v2v_net_trt = torch2trt(
                    root_v2v_net,
                    [x],
                    # fp16_mode=True,
                    use_onnx=True,
                )

            torch.save(root_v2v_net_trt.state_dict(), f'root_v2v_net_trt_{args.dtype}_{config.TEST.BATCH_SIZE}.pth')
        model.module.root_net.v2v_net = root_v2v_net_trt
        print(f'root_v2v_net_trt {args.dtype}_{config.TEST.BATCH_SIZE} set')

    # Pose
    if args.dtype != 'fp32':
        if os.path.exists(f'pose_v2v_net_trt_{args.dtype}_{config.TEST.BATCH_SIZE}.pth'):
            pose_v2v_net_trt = TRTModule()
            pose_v2v_net_trt.load_state_dict(torch.load(f'pose_v2v_net_trt_{args.dtype}_{config.TEST.BATCH_SIZE}.pth'))
            pose_v2v_net_trt.eval()
        else:
            print('trt pose_v2v_net')
            x = torch.ones(config.TEST.BATCH_SIZE, 15, 64, 64, 64).cuda()
            pose_v2v_net = model.module.pose_net.v2v_net
            if args.dtype == 'int8':
                calibration_data = CalibrationDataset('pose', config.TEST.BATCH_SIZE)
                pose_v2v_net_trt = torch2trt(
                    pose_v2v_net,
                    [x],
                    int8_mode=True,
                    # int8_calib_dataset=calibration_data,
                    use_onnx=True,
                    max_batch_size=1,
                )
            elif args.dtype == 'fp16':
                pose_v2v_net_trt = torch2trt(
                    pose_v2v_net,
                    [x],
                    # fp16_mode=True,
                    use_onnx=True,
                )
            torch.save(pose_v2v_net_trt.state_dict(), f'pose_v2v_net_trt_{args.dtype}_{config.TEST.BATCH_SIZE}.pth')
        model.module.pose_net.v2v_net = pose_v2v_net_trt
        print(f'pose_v2v_net_trt {args.dtype}_{config.TEST.BATCH_SIZE} set')

    preds, roots = [], []
    with torch.no_grad():
        try:
            for i, (inputs, targets_2d, weights_2d, targets_3d, meta, input_heatmap) in enumerate(tqdm(test_loader)):
                if 'panoptic' in config.DATASET.TEST_DATASET:
                    if with_ssv:
                        if vis_attn:
                            pred, _, grid_centers, attns = model(views1=inputs, meta1=meta, inference=True, visualize_attn=True)
                            attn_output_dir = os.path.join(final_output_dir, 'attn_vis')
                            if not os.path.exists(attn_output_dir):
                                os.makedirs(attn_output_dir)
                            for k in range(len(inputs)):
                                view_name = "view_{}".format(k + 1)
                                prefix = "{}_{:08}_{}".format(os.path.join(attn_output_dir, "valid"), i, view_name)
                                save_batch_heatmaps_multi(inputs[k], attns[k], "{}_hm_attn.jpg".format(prefix))
                        else:
                            pred, heatmaps, grid_centers = model(views1=inputs, meta1=meta, inference=True)
                    else:
                        pred, _, grid_centers, _, _, _ = model(views=inputs, meta=meta)
                elif 'campus' in config.DATASET.TEST_DATASET or 'shelf' in config.DATASET.TEST_DATASET:
                    pred, _, _, _, _, _ = model(meta=meta, input_heatmaps=input_heatmap)

                pred = pred.detach().cpu().numpy()
                root = grid_centers.detach().cpu().numpy()
                targets_3d = targets_3d[0].cpu().numpy()
                for b in range(pred.shape[0]):
                    preds.append(pred[b])
                    roots.append(root[b])
        except Exception as e:
            print(f'error at {i}')
            print(e)
        
        # save preds, roots as one npy file
        output = {
            'preds': preds,
            'roots': roots,
        }
        np.save(f'output_{args.dtype}.npy', output)

        timing_stats = model.module.timing_stats
        print(f"backbone: {(timing_stats['backbone'] / timing_stats['call_count']) * 1000}ms")
        print(f"root: {(timing_stats['root_net'] / timing_stats['call_count'])* 1000}ms")
        print(f"pose: {(timing_stats['pose_net'] / timing_stats['call_count']) * 1000}ms")
        print(timing_stats['call_count'])

        if 'panoptic' in config.DATASET.TEST_DATASET:
            mpjpe_threshold = np.arange(25, 155, 25)
            aps_all, recs_all, mpjpe_all, avg_recall_all = test_dataset.evaluate(preds, roots, final_output_dir)
            types_eval = ["pose", "root"]
            for aps, recs, mpjpe, recall, type_eval in zip(aps_all, recs_all, mpjpe_all, avg_recall_all, types_eval):
                tb = PrettyTable()
                print(f'Type: {type_eval}')
                tb.field_names = ['Threshold/mm'] + [f'{i}' for i in mpjpe_threshold]
                tb.add_row(['AP'] + [f'{ap * 100:.2f}' for ap in aps])
                tb.add_row(['Recall'] + [f'{re * 100:.2f}' for re in recs])
                print(tb)
                print(f'MPJPE: {mpjpe:.2f}mm')
                print(f'recall@500: {recall:.4f}, {np.array(recs).mean()}')
        else:
            tb = PrettyTable()
            actor_pcp, avg_pcp, bone_person_pcp, _ = test_dataset.evaluate(preds)
            tb.field_names = ['Bone Group'] + [f'Actor {i+1}' for i in range(len(actor_pcp))] + ['Average']
            for k, v in bone_person_pcp.items():
                tb.add_row([k] + [f'{i*100:.1f}' for i in v] + [f'{np.mean(v)*100:.1f}'])
            tb.add_row(['Total'] + [f'{i*100:.1f}' for i in actor_pcp] + [f'{avg_pcp*100:.1f}'])
            print(tb)


if __name__ == "__main__":
    default_argv = [
        '--cfg', 'configs/panoptic_ssl/resnet50/cam5_posenet.yaml',
        '--with-ssv',
        '--test-file', 'models/cam5_posenet.pth.tar',
        '--dtype', 'fp32',
    ]
    sys.argv.extend(default_argv)
    main()
